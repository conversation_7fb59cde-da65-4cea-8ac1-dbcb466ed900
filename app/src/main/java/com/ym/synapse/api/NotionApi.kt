package com.ym.synapse.api

import com.google.gson.annotations.SerializedName
import okhttp3.MultipartBody
import okhttp3.RequestBody
import retrofit2.Response
import retrofit2.http.*

/**
 * Notion API接口
 */
interface NotionApiService {
    
    /**
     * 创建页面
     */
    @POST("v1/pages")
    suspend fun createPage(
        @Header("Authorization") authorization: String,
        @Header("Notion-Version") notionVersion: String = "2022-06-28",
        @Header("Content-Type") contentType: String = "application/json",
        @Body request: CreatePageRequest
    ): Response<CreatePageResponse>
    
    /**
     * 上传文件
     */
    @Multipart
    @POST("v1/files")
    suspend fun uploadFile(
        @Header("Authorization") authorization: String,
        @Header("Notion-Version") notionVersion: String = "2022-06-28",
        @Part file: MultipartBody.Part
    ): Response<UploadFileResponse>
    
    /**
     * 获取数据库信息
     */
    @GET("v1/databases/{database_id}")
    suspend fun getDatabase(
        @Header("Authorization") authorization: String,
        @Header("Notion-Version") notionVersion: String = "2022-06-28",
        @Path("database_id") databaseId: String
    ): Response<DatabaseResponse>
}

/**
 * 创建页面请求
 */
data class CreatePageRequest(
    @SerializedName("parent")
    val parent: Parent,
    
    @SerializedName("properties")
    val properties: Map<String, Property>,
    
    @SerializedName("children")
    val children: List<Block> = emptyList()
)

/**
 * 父级对象（数据库）
 */
data class Parent(
    @SerializedName("database_id")
    val databaseId: String
)

/**
 * 属性基类
 */
sealed class Property {
    data class Title(
        @SerializedName("title")
        val title: List<RichText>
    ) : Property()
    
    data class RichTextProperty(
        @SerializedName("rich_text")
        val richText: List<RichText>
    ) : Property()
    
    data class MultiSelect(
        @SerializedName("multi_select")
        val multiSelect: List<SelectOption>
    ) : Property()
    
    data class Files(
        @SerializedName("files")
        val files: List<FileObject>
    ) : Property()
}

/**
 * 富文本对象
 */
data class RichText(
    @SerializedName("type")
    val type: String = "text",
    
    @SerializedName("text")
    val text: TextContent,
    
    @SerializedName("annotations")
    val annotations: Annotations? = null
)

/**
 * 文本内容
 */
data class TextContent(
    @SerializedName("content")
    val content: String,
    
    @SerializedName("link")
    val link: String? = null
)

/**
 * 文本注释（格式）
 */
data class Annotations(
    @SerializedName("bold")
    val bold: Boolean = false,
    
    @SerializedName("italic")
    val italic: Boolean = false,
    
    @SerializedName("strikethrough")
    val strikethrough: Boolean = false,
    
    @SerializedName("underline")
    val underline: Boolean = false,
    
    @SerializedName("code")
    val code: Boolean = false,
    
    @SerializedName("color")
    val color: String = "default"
)

/**
 * 选择选项
 */
data class SelectOption(
    @SerializedName("name")
    val name: String,
    
    @SerializedName("color")
    val color: String? = null
)

/**
 * 文件对象
 */
data class FileObject(
    @SerializedName("name")
    val name: String,
    
    @SerializedName("type")
    val type: String = "external",
    
    @SerializedName("external")
    val external: ExternalFile? = null,
    
    @SerializedName("file")
    val file: InternalFile? = null
)

/**
 * 外部文件
 */
data class ExternalFile(
    @SerializedName("url")
    val url: String
)

/**
 * 内部文件
 */
data class InternalFile(
    @SerializedName("url")
    val url: String,
    
    @SerializedName("expiry_time")
    val expiryTime: String
)

/**
 * 块对象（页面内容）
 */
sealed class Block {
    data class Paragraph(
        @SerializedName("object")
        val objectType: String = "block",
        
        @SerializedName("type")
        val type: String = "paragraph",
        
        @SerializedName("paragraph")
        val paragraph: ParagraphBlock
    ) : Block()
    
    data class Image(
        @SerializedName("object")
        val objectType: String = "block",
        
        @SerializedName("type")
        val type: String = "image",
        
        @SerializedName("image")
        val image: ImageBlock
    ) : Block()
    
    data class BulletedListItem(
        @SerializedName("object")
        val objectType: String = "block",
        
        @SerializedName("type")
        val type: String = "bulleted_list_item",
        
        @SerializedName("bulleted_list_item")
        val bulletedListItem: BulletedListItemBlock
    ) : Block()
}

/**
 * 段落块
 */
data class ParagraphBlock(
    @SerializedName("rich_text")
    val richText: List<RichText>,
    
    @SerializedName("color")
    val color: String = "default"
)

/**
 * 图片块
 */
data class ImageBlock(
    @SerializedName("type")
    val type: String = "external",
    
    @SerializedName("external")
    val external: ExternalFile? = null,
    
    @SerializedName("file")
    val file: InternalFile? = null,
    
    @SerializedName("caption")
    val caption: List<RichText> = emptyList()
)

/**
 * 项目符号列表项块
 */
data class BulletedListItemBlock(
    @SerializedName("rich_text")
    val richText: List<RichText>,
    
    @SerializedName("color")
    val color: String = "default"
)

/**
 * 创建页面响应
 */
data class CreatePageResponse(
    @SerializedName("object")
    val objectType: String,
    
    @SerializedName("id")
    val id: String,
    
    @SerializedName("created_time")
    val createdTime: String,
    
    @SerializedName("last_edited_time")
    val lastEditedTime: String,
    
    @SerializedName("url")
    val url: String,
    
    @SerializedName("properties")
    val properties: Map<String, Any>
)

/**
 * 上传文件响应
 */
data class UploadFileResponse(
    @SerializedName("object")
    val objectType: String,
    
    @SerializedName("id")
    val id: String,
    
    @SerializedName("name")
    val name: String,
    
    @SerializedName("url")
    val url: String
)

/**
 * 数据库响应
 */
data class DatabaseResponse(
    @SerializedName("object")
    val objectType: String,
    
    @SerializedName("id")
    val id: String,
    
    @SerializedName("title")
    val title: List<RichText>,
    
    @SerializedName("properties")
    val properties: Map<String, Any>
)
