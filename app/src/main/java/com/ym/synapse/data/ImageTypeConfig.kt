package com.ym.synapse.data

import android.content.Context
import android.content.SharedPreferences
import android.util.Log
import com.ym.synapse.*

/**
 * 图片类型配置数据类
 */
data class ImageTypeConfig(
    val type: String,
    val displayName: String,
    val apiUrl: String = "",
    val apiKey: String = "",
    val modelId: String = "",
    val prompt: String = ""
)

/**
 * 模型配置管理器
 */
object ModelConfigManager {
    
    /**
     * 获取所有图片类型的配置
     */
    fun getAllImageTypeConfigs(context: Context): List<ImageTypeConfig> {
        val sharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        
        return listOf(
            ImageTypeConfig(
                type = IMAGE_TYPE_TEXT_HEAVY,
                displayName = "文字密集型",
                apiUrl = sharedPreferences.getString(KEY_TEXT_HEAVY_API_URL, "") ?: "",
                apiKey = sharedPreferences.getString(KEY_TEXT_HEAVY_API_KEY, "") ?: "",
                modelId = sharedPreferences.getString(KEY_TEXT_HEAVY_MODEL_ID, "") ?: "",
                prompt = sharedPreferences.getString(KEY_TEXT_HEAVY_PROMPT, DEFAULT_TEXT_HEAVY_PROMPT) ?: DEFAULT_TEXT_HEAVY_PROMPT
            ),
            ImageTypeConfig(
                type = IMAGE_TYPE_RICH_CONTENT,
                displayName = "富内容型",
                apiUrl = sharedPreferences.getString(KEY_RICH_CONTENT_API_URL, "") ?: "",
                apiKey = sharedPreferences.getString(KEY_RICH_CONTENT_API_KEY, "") ?: "",
                modelId = sharedPreferences.getString(KEY_RICH_CONTENT_MODEL_ID, "") ?: "",
                prompt = sharedPreferences.getString(KEY_RICH_CONTENT_PROMPT, DEFAULT_RICH_CONTENT_PROMPT) ?: DEFAULT_RICH_CONTENT_PROMPT
            ),
            ImageTypeConfig(
                type = IMAGE_TYPE_SIMPLE_IMAGE,
                displayName = "简单图片型",
                apiUrl = sharedPreferences.getString(KEY_SIMPLE_IMAGE_API_URL, "") ?: "",
                apiKey = sharedPreferences.getString(KEY_SIMPLE_IMAGE_API_KEY, "") ?: "",
                modelId = sharedPreferences.getString(KEY_SIMPLE_IMAGE_MODEL_ID, "") ?: "",
                prompt = sharedPreferences.getString(KEY_SIMPLE_IMAGE_PROMPT, DEFAULT_SIMPLE_IMAGE_PROMPT) ?: DEFAULT_SIMPLE_IMAGE_PROMPT
            )
        )
    }
    
    /**
     * 保存图片类型配置
     */
    fun saveImageTypeConfig(context: Context, config: ImageTypeConfig) {
        val sharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        
        with(sharedPreferences.edit()) {
            when (config.type) {
                IMAGE_TYPE_TEXT_HEAVY -> {
                    putString(KEY_TEXT_HEAVY_API_URL, config.apiUrl)
                    putString(KEY_TEXT_HEAVY_API_KEY, config.apiKey)
                    putString(KEY_TEXT_HEAVY_MODEL_ID, config.modelId)
                    putString(KEY_TEXT_HEAVY_PROMPT, config.prompt)
                }
                IMAGE_TYPE_RICH_CONTENT -> {
                    putString(KEY_RICH_CONTENT_API_URL, config.apiUrl)
                    putString(KEY_RICH_CONTENT_API_KEY, config.apiKey)
                    putString(KEY_RICH_CONTENT_MODEL_ID, config.modelId)
                    putString(KEY_RICH_CONTENT_PROMPT, config.prompt)
                }
                IMAGE_TYPE_SIMPLE_IMAGE -> {
                    putString(KEY_SIMPLE_IMAGE_API_URL, config.apiUrl)
                    putString(KEY_SIMPLE_IMAGE_API_KEY, config.apiKey)
                    putString(KEY_SIMPLE_IMAGE_MODEL_ID, config.modelId)
                    putString(KEY_SIMPLE_IMAGE_PROMPT, config.prompt)
                }
            }
            apply()
        }
    }
    
    /**
     * 获取当前模型配置模式
     */
    fun getModelConfigMode(context: Context): String {
        val sharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        return sharedPreferences.getString(KEY_MODEL_CONFIG_MODE, MODEL_CONFIG_SHARED) ?: MODEL_CONFIG_SHARED
    }
    
    /**
     * 设置模型配置模式
     */
    fun setModelConfigMode(context: Context, mode: String) {
        val sharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        with(sharedPreferences.edit()) {
            putString(KEY_MODEL_CONFIG_MODE, mode)
            apply()
        }
    }
    
    /**
     * 根据图片类型获取对应的配置
     */
    fun getConfigForImageType(context: Context, imageType: String): ImageTypeConfig? {
        val mode = getModelConfigMode(context)
        Log.d("ModelConfigManager", "Getting config for image type: $imageType, mode: $mode")

        val config = if (mode == MODEL_CONFIG_SHARED) {
            // 共用模式：使用全局配置
            val sharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            ImageTypeConfig(
                type = imageType,
                displayName = getDisplayNameForType(imageType),
                apiUrl = sharedPreferences.getString(KEY_API_URL, "") ?: "",
                apiKey = sharedPreferences.getString(KEY_API_KEY, "") ?: "",
                modelId = sharedPreferences.getString(KEY_AI_OCR_MODEL_ID, "") ?: "",
                prompt = sharedPreferences.getString(KEY_AI_OCR_PROMPT, DEFAULT_AI_OCR_PROMPT) ?: DEFAULT_AI_OCR_PROMPT
            )
        } else {
            // 分离模式：使用对应类型的配置
            getAllImageTypeConfigs(context).find { it.type == imageType }
        }

        Log.d("ModelConfigManager", "Config for $imageType: apiUrl=${config?.apiUrl?.take(20)}..., hasApiKey=${!config?.apiKey.isNullOrEmpty()}")
        return config
    }
    
    private fun getDisplayNameForType(type: String): String {
        return when (type) {
            IMAGE_TYPE_TEXT_HEAVY -> "文字密集型"
            IMAGE_TYPE_RICH_CONTENT -> "富内容型"
            IMAGE_TYPE_SIMPLE_IMAGE -> "简单图片型"
            else -> type
        }
    }
}
