package com.ym.synapse.service

import android.content.Context
import android.util.Log
import com.ym.synapse.KEY_NOTION_DATABASE_ID
import com.ym.synapse.KEY_NOTION_TOKEN
import com.ym.synapse.PREFS_NAME
import com.ym.synapse.api.*
import com.ym.synapse.data.NotionAnalysisResult
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.MultipartBody
import okhttp3.OkHttpClient
import okhttp3.RequestBody.Companion.asRequestBody
import retrofit2.HttpException
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.io.File
import java.util.concurrent.TimeUnit

/**
 * Notion服务类
 * 负责与Notion API的交互
 */
class NotionService private constructor() {
    
    companion object {
        private const val TAG = "NotionService"
        private const val NOTION_API_BASE_URL = "https://api.notion.com/"
        
        @Volatile
        private var INSTANCE: NotionService? = null
        
        fun getInstance(): NotionService {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: NotionService().also { INSTANCE = it }
            }
        }
    }
    
    private val client = OkHttpClient.Builder()
        .connectTimeout(30, TimeUnit.SECONDS)
        .readTimeout(60, TimeUnit.SECONDS)
        .writeTimeout(60, TimeUnit.SECONDS)
        .build()
    
    private val api = Retrofit.Builder()
        .baseUrl(NOTION_API_BASE_URL)
        .client(client)
        .addConverterFactory(GsonConverterFactory.create())
        .build()
        .create(NotionApiService::class.java)
    
    /**
     * 发送分析结果到Notion
     */
    suspend fun sendToNotion(
        context: Context,
        analysisResult: NotionAnalysisResult,
        imagePath: String? = null
    ): NotionResult {
        return withContext(Dispatchers.IO) {
            try {
                val sharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
                val token = sharedPreferences.getString(KEY_NOTION_TOKEN, "") ?: ""
                val databaseId = sharedPreferences.getString(KEY_NOTION_DATABASE_ID, "") ?: ""
                
                if (token.isEmpty() || databaseId.isEmpty()) {
                    return@withContext NotionResult.Error("Notion配置不完整，请检查Token和Database ID")
                }
                
                Log.d(TAG, "Sending to Notion: ${analysisResult.title}")
                
                // 1. 如果有图片，先上传图片
                var imageUrl: String? = null
                if (!imagePath.isNullOrEmpty()) {
                    val uploadResult = uploadImage(token, imagePath)
                    when (uploadResult) {
                        is NotionResult.Success -> {
                            imageUrl = uploadResult.data as? String
                            Log.d(TAG, "Image uploaded successfully: $imageUrl")
                        }
                        is NotionResult.Error -> {
                            Log.w(TAG, "Failed to upload image: ${uploadResult.message}")
                            // 继续创建页面，但不包含图片
                        }
                    }
                }
                
                // 2. 创建页面
                val createPageResult = createPage(token, databaseId, analysisResult, imageUrl)
                
                when (createPageResult) {
                    is NotionResult.Success -> {
                        val response = createPageResult.data as CreatePageResponse
                        Log.d(TAG, "Page created successfully: ${response.url}")
                        NotionResult.Success(response.url)
                    }
                    is NotionResult.Error -> {
                        Log.e(TAG, "Failed to create page: ${createPageResult.message}")
                        createPageResult
                    }
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "Error sending to Notion", e)
                NotionResult.Error("发送到Notion失败: ${e.message}")
            }
        }
    }
    
    /**
     * 上传图片到Notion
     */
    private suspend fun uploadImage(token: String, imagePath: String): NotionResult {
        return try {
            val imageFile = File(imagePath)
            if (!imageFile.exists()) {
                return NotionResult.Error("图片文件不存在")
            }
            
            val requestFile = imageFile.asRequestBody("image/*".toMediaTypeOrNull())
            val body = MultipartBody.Part.createFormData("file", imageFile.name, requestFile)
            
            val response = api.uploadFile("Bearer $token", file = body)
            
            if (response.isSuccessful) {
                val uploadResponse = response.body()
                if (uploadResponse != null) {
                    NotionResult.Success(uploadResponse.url)
                } else {
                    NotionResult.Error("上传响应为空")
                }
            } else {
                val errorBody = response.errorBody()?.string()
                NotionResult.Error("上传失败: HTTP ${response.code()} - $errorBody")
            }
        } catch (e: HttpException) {
            NotionResult.Error("上传失败: HTTP ${e.code()}")
        } catch (e: Exception) {
            NotionResult.Error("上传失败: ${e.message}")
        }
    }
    
    /**
     * 创建Notion页面
     */
    private suspend fun createPage(
        token: String,
        databaseId: String,
        analysisResult: NotionAnalysisResult,
        imageUrl: String? = null
    ): NotionResult {
        return try {
            // 构建页面属性
            val properties = mutableMapOf<String, Property>()
            
            // 标题属性
            properties["Name"] = Property.Title(
                title = listOf(
                    RichText(
                        text = TextContent(content = analysisResult.title)
                    )
                )
            )
            
            // 摘要属性
            properties["Summary"] = Property.RichTextProperty(
                richText = listOf(
                    RichText(
                        text = TextContent(content = analysisResult.summary)
                    )
                )
            )
            
            // 标签属性
            if (analysisResult.tags.isNotEmpty()) {
                properties["Tags"] = Property.MultiSelect(
                    multiSelect = analysisResult.tags.map { tag ->
                        SelectOption(name = tag)
                    }
                )
            }
            
            // 构建页面内容块
            val children = mutableListOf<Block>()
            
            // 添加图片块（如果有）
            if (!imageUrl.isNullOrEmpty()) {
                children.add(
                    Block.Image(
                        image = ImageBlock(
                            type = "external",
                            external = ExternalFile(url = imageUrl),
                            caption = listOf(
                                RichText(
                                    text = TextContent(content = "截图")
                                )
                            )
                        )
                    )
                )
            }
            
            // 添加摘要段落
            children.add(
                Block.Paragraph(
                    paragraph = ParagraphBlock(
                        richText = listOf(
                            RichText(
                                text = TextContent(content = analysisResult.summary)
                            )
                        )
                    )
                )
            )
            
            // 添加行动项（如果有）
            if (analysisResult.actionItems.isNotEmpty()) {
                // 添加行动项标题
                children.add(
                    Block.Paragraph(
                        paragraph = ParagraphBlock(
                            richText = listOf(
                                RichText(
                                    text = TextContent(content = "行动项："),
                                    annotations = Annotations(bold = true)
                                )
                            )
                        )
                    )
                )
                
                // 添加每个行动项
                analysisResult.actionItems.forEach { item ->
                    children.add(
                        Block.BulletedListItem(
                            bulletedListItem = BulletedListItemBlock(
                                richText = listOf(
                                    RichText(
                                        text = TextContent(content = item)
                                    )
                                )
                            )
                        )
                    )
                }
            }
            
            // 创建页面请求
            val request = CreatePageRequest(
                parent = Parent(databaseId = databaseId),
                properties = properties,
                children = children
            )
            
            val response = api.createPage("Bearer $token", request = request)
            
            if (response.isSuccessful) {
                val createResponse = response.body()
                if (createResponse != null) {
                    NotionResult.Success(createResponse)
                } else {
                    NotionResult.Error("创建页面响应为空")
                }
            } else {
                val errorBody = response.errorBody()?.string()
                NotionResult.Error("创建页面失败: HTTP ${response.code()} - $errorBody")
            }
            
        } catch (e: HttpException) {
            NotionResult.Error("创建页面失败: HTTP ${e.code()}")
        } catch (e: Exception) {
            NotionResult.Error("创建页面失败: ${e.message}")
        }
    }
    
    /**
     * 测试Notion连接
     */
    suspend fun testConnection(context: Context): NotionResult {
        return withContext(Dispatchers.IO) {
            try {
                val sharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
                val token = sharedPreferences.getString(KEY_NOTION_TOKEN, "") ?: ""
                val databaseId = sharedPreferences.getString(KEY_NOTION_DATABASE_ID, "") ?: ""
                
                if (token.isEmpty() || databaseId.isEmpty()) {
                    return@withContext NotionResult.Error("Notion配置不完整")
                }
                
                val response = api.getDatabase("Bearer $token", databaseId)
                
                if (response.isSuccessful) {
                    NotionResult.Success("连接成功")
                } else {
                    val errorBody = response.errorBody()?.string()
                    NotionResult.Error("连接失败: HTTP ${response.code()} - $errorBody")
                }
            } catch (e: Exception) {
                NotionResult.Error("连接失败: ${e.message}")
            }
        }
    }
}

/**
 * Notion操作结果
 */
sealed class NotionResult {
    data class Success(val data: Any) : NotionResult()
    data class Error(val message: String) : NotionResult()
}
