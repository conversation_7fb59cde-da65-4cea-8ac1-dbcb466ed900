package com.ym.synapse.service

import android.content.Context
import android.util.Log
import com.ym.synapse.KEY_NOTION_DATABASE_ID
import com.ym.synapse.KEY_NOTION_TOKEN
import com.ym.synapse.PREFS_NAME
import com.ym.synapse.api.*
import com.ym.synapse.data.NotionAnalysisResult
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.MultipartBody
import okhttp3.OkHttpClient
import okhttp3.RequestBody.Companion.asRequestBody
import retrofit2.HttpException
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.io.File
import java.util.concurrent.TimeUnit

/**
 * Notion服务类
 * 负责与Notion API的交互
 */
class NotionService private constructor() {
    
    companion object {
        private const val TAG = "NotionService"
        private const val NOTION_API_BASE_URL = "https://api.notion.com/"

        @Volatile
        private var INSTANCE: NotionService? = null

        fun getInstance(): NotionService {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: NotionService().also { INSTANCE = it }
            }
        }

        /**
         * 格式化数据库ID为Notion API要求的格式
         */
        private fun formatDatabaseId(databaseId: String): String {
            // 移除所有连字符和空格
            val cleanId = databaseId.replace("-", "").replace(" ", "")

            // 如果长度是32位，添加连字符格式化为UUID格式
            return if (cleanId.length == 32) {
                "${cleanId.substring(0, 8)}-${cleanId.substring(8, 12)}-${cleanId.substring(12, 16)}-${cleanId.substring(16, 20)}-${cleanId.substring(20, 32)}"
            } else {
                cleanId // 如果不是32位，直接返回
            }
        }
    }
    
    private val client = OkHttpClient.Builder()
        .connectTimeout(30, TimeUnit.SECONDS)
        .readTimeout(60, TimeUnit.SECONDS)
        .writeTimeout(60, TimeUnit.SECONDS)
        .build()
    
    private val api = Retrofit.Builder()
        .baseUrl(NOTION_API_BASE_URL)
        .client(client)
        .addConverterFactory(GsonConverterFactory.create())
        .build()
        .create(NotionApiService::class.java)
    
    /**
     * 发送分析结果到Notion
     */
    suspend fun sendToNotion(
        context: Context,
        analysisResult: NotionAnalysisResult,
        imagePath: String? = null
    ): NotionResult {
        return withContext(Dispatchers.IO) {
            try {
                val sharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
                val token = sharedPreferences.getString(KEY_NOTION_TOKEN, "") ?: ""
                val rawDatabaseId = sharedPreferences.getString(KEY_NOTION_DATABASE_ID, "") ?: ""

                if (token.isEmpty() || rawDatabaseId.isEmpty()) {
                    return@withContext NotionResult.Error("Notion配置不完整，请检查Token和Database ID")
                }

                val databaseId = formatDatabaseId(rawDatabaseId)
                Log.d(TAG, "Using formatted database ID: $databaseId")
                
                Log.d(TAG, "Sending to Notion: ${analysisResult.title}")
                Log.d(TAG, "Token length: ${token.length}, starts with 'secret_': ${token.startsWith("secret_")}")
                Log.d(TAG, "Database ID: $databaseId")

                // 注意：Notion API不支持直接文件上传，我们暂时跳过图片上传
                // 如果需要图片，可以考虑先上传到其他服务（如图床）再引用URL
                var imageUrl: String? = null
                Log.d(TAG, "Skipping image upload (Notion API limitation)")

                // 2. 创建页面
                val createPageResult = createPage(token, databaseId, analysisResult, imageUrl)
                
                when (createPageResult) {
                    is NotionResult.Success -> {
                        val response = createPageResult.data as CreatePageResponse
                        Log.d(TAG, "Page created successfully: ${response.url}")
                        NotionResult.Success(response.url)
                    }
                    is NotionResult.Error -> {
                        Log.e(TAG, "Failed to create page: ${createPageResult.message}")
                        createPageResult
                    }
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "Error sending to Notion", e)
                NotionResult.Error("发送到Notion失败: ${e.message}")
            }
        }
    }
    

    
    /**
     * 创建Notion页面
     */
    private suspend fun createPage(
        token: String,
        databaseId: String,
        analysisResult: NotionAnalysisResult,
        imageUrl: String? = null
    ): NotionResult {
        return try {
            // 构建页面属性 - 使用通用的属性名称
            val properties = mutableMapOf<String, Property>()

            // 标题属性 - 使用"标题"或"Name"
            properties["标题"] = Property.Title(
                title = listOf(
                    RichText(
                        text = TextContent(content = analysisResult.title)
                    )
                )
            )

            // 摘要属性
            properties["摘要"] = Property.RichTextProperty(
                richText = listOf(
                    RichText(
                        text = TextContent(content = analysisResult.summary)
                    )
                )
            )

            // 标签属性
            if (analysisResult.tags.isNotEmpty()) {
                properties["标签"] = Property.MultiSelect(
                    multiSelect = analysisResult.tags.map { tag ->
                        SelectOption(name = tag)
                    }
                )
            }
            
            // 构建页面内容块
            val children = mutableListOf<Block>()
            
            // 添加摘要段落
            children.add(
                Block.Paragraph(
                    paragraph = ParagraphBlock(
                        richText = listOf(
                            RichText(
                                text = TextContent(content = analysisResult.summary)
                            )
                        )
                    )
                )
            )
            
            // 添加行动项（如果有）
            if (analysisResult.actionItems.isNotEmpty()) {
                // 添加行动项标题
                children.add(
                    Block.Paragraph(
                        paragraph = ParagraphBlock(
                            richText = listOf(
                                RichText(
                                    text = TextContent(content = "行动项："),
                                    annotations = Annotations(bold = true)
                                )
                            )
                        )
                    )
                )
                
                // 添加每个行动项
                analysisResult.actionItems.forEach { item ->
                    children.add(
                        Block.BulletedListItem(
                            bulletedListItem = BulletedListItemBlock(
                                richText = listOf(
                                    RichText(
                                        text = TextContent(content = item)
                                    )
                                )
                            )
                        )
                    )
                }
            }
            
            // 创建页面请求
            val request = CreatePageRequest(
                parent = Parent(databaseId = databaseId),
                properties = properties,
                children = children
            )
            
            val response = api.createPage("Bearer $token", request = request)

            Log.d(TAG, "Create page response code: ${response.code()}")

            if (response.isSuccessful) {
                val createResponse = response.body()
                if (createResponse != null) {
                    Log.d(TAG, "Page created successfully: ${createResponse.url}")
                    NotionResult.Success(createResponse)
                } else {
                    Log.e(TAG, "Create page response body is null")
                    NotionResult.Error("创建页面响应为空")
                }
            } else {
                val errorBody = response.errorBody()?.string()
                Log.e(TAG, "Create page failed: HTTP ${response.code()} - $errorBody")

                val errorMessage = when (response.code()) {
                    400 -> "请求格式错误，可能是数据库属性不匹配。请检查数据库是否有'标题'、'摘要'、'标签'属性"
                    401 -> "认证失败，请检查Integration Token"
                    403 -> "权限不足，请确认Integration已被授权访问该数据库"
                    404 -> "数据库未找到，请检查Database ID"
                    else -> "创建页面失败: HTTP ${response.code()} - ${errorBody ?: response.message()}"
                }

                NotionResult.Error(errorMessage)
            }
            
        } catch (e: HttpException) {
            NotionResult.Error("创建页面失败: HTTP ${e.code()}")
        } catch (e: Exception) {
            NotionResult.Error("创建页面失败: ${e.message}")
        }
    }
    
    /**
     * 测试Notion连接
     */
    suspend fun testConnection(context: Context): NotionResult {
        return withContext(Dispatchers.IO) {
            try {
                val sharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
                val token = sharedPreferences.getString(KEY_NOTION_TOKEN, "") ?: ""
                val rawDatabaseId = sharedPreferences.getString(KEY_NOTION_DATABASE_ID, "") ?: ""

                if (token.isEmpty() || rawDatabaseId.isEmpty()) {
                    return@withContext NotionResult.Error("Notion配置不完整")
                }

                val databaseId = formatDatabaseId(rawDatabaseId)
                Log.d(TAG, "Testing connection with database ID: $databaseId")

                val response = api.getDatabase("Bearer $token", databaseId = databaseId)

                Log.d(TAG, "API response code: ${response.code()}")
                Log.d(TAG, "API response message: ${response.message()}")

                if (response.isSuccessful) {
                    val database = response.body()
                    Log.d(TAG, "Database retrieved successfully: ${database?.title}")
                    NotionResult.Success("连接成功！数据库: ${database?.title?.firstOrNull()?.text?.content ?: "未知"}")
                } else {
                    val errorBody = response.errorBody()?.string()
                    Log.e(TAG, "Connection failed: HTTP ${response.code()} - $errorBody")

                    val errorMessage = when (response.code()) {
                        401 -> "认证失败，请检查Integration Token是否正确"
                        404 -> "数据库未找到，请检查Database ID是否正确，或确认Integration已被授权访问该数据库"
                        403 -> "权限不足，请确认Integration已被授权访问该数据库"
                        else -> "连接失败: HTTP ${response.code()} - ${errorBody ?: response.message()}"
                    }

                    NotionResult.Error(errorMessage)
                }
            } catch (e: Exception) {
                NotionResult.Error("连接失败: ${e.message}")
            }
        }
    }
}

/**
 * Notion操作结果
 */
sealed class NotionResult {
    data class Success(val data: Any) : NotionResult()
    data class Error(val message: String) : NotionResult()
}
