package com.ym.synapse

// SharedPreferences keys
const val PREFS_NAME = "ai_ocr_prefs"
const val KEY_API_URL = "api_url"
const val KEY_API_KEY = "api_key"
const val KEY_AI_OCR_MODEL_ID = "ai_ocr_model_id"
const val KEY_AI_OCR_PROMPT = "ai_ocr_prompt"
const val DEFAULT_AI_OCR_PROMPT = "Extract all text from this image."
const val DEFAULT_AI_OCR_MODEL_ID = ""

// 模型配置模式
const val KEY_MODEL_CONFIG_MODE = "model_config_mode"
const val MODEL_CONFIG_SHARED = "shared"
const val MODEL_CONFIG_SEPARATE = "separate"

// 图片类型定义
const val IMAGE_TYPE_TEXT_HEAVY = "Text-Heavy"
const val IMAGE_TYPE_RICH_CONTENT = "Rich-Content"
const val IMAGE_TYPE_SIMPLE_IMAGE = "Simple-Image"

// 各类型的配置键
// Text-Heavy 配置
const val KEY_TEXT_HEAVY_API_URL = "text_heavy_api_url"
const val KEY_TEXT_HEAVY_API_KEY = "text_heavy_api_key"
const val KEY_TEXT_HEAVY_MODEL_ID = "text_heavy_model_id"
const val KEY_TEXT_HEAVY_PROMPT = "text_heavy_prompt"

// Rich-Content 配置
const val KEY_RICH_CONTENT_API_URL = "rich_content_api_url"
const val KEY_RICH_CONTENT_API_KEY = "rich_content_api_key"
const val KEY_RICH_CONTENT_MODEL_ID = "rich_content_model_id"
const val KEY_RICH_CONTENT_PROMPT = "rich_content_prompt"

// Simple-Image 配置
const val KEY_SIMPLE_IMAGE_API_URL = "simple_image_api_url"
const val KEY_SIMPLE_IMAGE_API_KEY = "simple_image_api_key"
const val KEY_SIMPLE_IMAGE_MODEL_ID = "simple_image_model_id"
const val KEY_SIMPLE_IMAGE_PROMPT = "simple_image_prompt"

// Notion 配置
const val KEY_NOTION_TOKEN = "notion_token"
const val KEY_NOTION_DATABASE_ID = "notion_database_id"
const val KEY_NOTION_ENABLED = "notion_enabled"
const val KEY_NOTION_AUTO_SYNC = "notion_auto_sync"

// 默认提示词 - 针对Notion集成优化
const val DEFAULT_TEXT_HEAVY_PROMPT = """# 角色
你是一位高效的信息助理和知识管理专家。

# 任务
我将提供一张包含大量文字内容的图片。请你完成以下任务，并严格按照指定的 JSON 格式返回结果：

1. **提炼标题 (title)**：根据图片中文本的核心内容，生成一个不超过 15 个字的精炼标题。
2. **内容摘要 (summary)**：用清晰、有条理的语言总结图片中的所有关键文字信息，保持100-300字的长度。
3. **识别行动项 (action_items)**：如果文本中包含任何可以转化为具体行动或待办事项的信息，将其提取出来。如果没有，则返回空列表。
4. **生成标签 (tags)**：根据内容生成 3-5 个有助于分类和搜索的标签。

# 输出要求
- 严格按照 JSON 格式输出
- 准确识别并提取所有可见文字
- 保持原文的准确性，包括标点符号、数字、特殊字符
- 如果包含代码，保持代码的缩进和格式
- 如果包含表格，用合适的方式表示表格结构

# 输出格式
```json
{
  "title": "不超过15字的标题",
  "summary": "100-300字的详细内容摘要",
  "action_items": ["行动项1", "行动项2"],
  "tags": ["标签1", "标签2", "标签3"]
}
```"""

const val DEFAULT_RICH_CONTENT_PROMPT = """# 角色
你是一位顶级的知识管理专家，精通从各种信息中提取核心价值。

# 任务
你的任务是分析我提供的手机截图，并严格按照指定的 JSON 格式返回一个结构化的摘要。这个摘要将用于在 Notion 中创建一个新的页面。

# 分析指令
1. **全局理解**：综合分析截图中的所有视觉元素，包括但不限于：文字内容、UI 布局、图表、数据、图片等。
2. **提炼标题 (title)**：根据截图最核心的主题，生成一个不超过 15 个字的、精炼的页面标题。
3. **深度总结 (summary)**：用清晰、有条理的语言总结截图中的所有关键信息，保持100-300字的长度。摘要应完整、准确，让人无需查看原图也能理解核心内容。
4. **识别行动项 (action_items)**：如果截图中包含任何可以转化为具体行动或待办事项的信息（如"明天开会"、"记得回复邮件"、"需要购买的物品"等），将其提取出来。如果没有，则返回空列表。
5. **生成标签 (tags)**：根据内容生成 3-5 个有助于分类和搜索的标签（例如："技术文章"、"会议纪要"、"购物清单"、"项目管理"等）。

# 输出格式
```json
{
  "title": "不超过15字的标题",
  "summary": "100-300字的详细内容摘要",
  "action_items": ["行动项1", "行动项2"],
  "tags": ["标签1", "标签2", "标签3"]
}
```"""

const val DEFAULT_SIMPLE_IMAGE_PROMPT = """# 角色
你是一位顶级的知识管理专家，精通从各种信息中提取核心价值。

# 任务
你的任务是分析我提供的图片，并严格按照指定的 JSON 格式返回一个结构化的摘要。这个摘要将用于在 Notion 中创建一个新的页面。

# 分析指令
1. **全局理解**：仔细观察图片中的所有视觉元素，包括主要对象、场景、颜色、构图等。
2. **提炼标题 (title)**：根据图片的主要内容，生成一个不超过 15 个字的、精炼的描述性标题。
3. **深度描述 (summary)**：用清晰、生动的语言描述图片内容，保持100-300字的长度。包括主要对象、场景特征、颜色搭配、构图风格、情感氛围等。
4. **识别行动项 (action_items)**：如果图片中包含任何可以转化为具体行动的信息（如地点推荐、购买建议等），将其提取出来。通常情况下为空列表。
5. **生成标签 (tags)**：根据图片内容生成 3-5 个有助于分类和搜索的标签（例如："风景摄影"、"美食记录"、"旅行回忆"、"艺术作品"等）。

# 输出格式
```json
{
  "title": "不超过15字的标题",
  "summary": "100-300字的详细图片描述",
  "action_items": ["行动项1", "行动项2"],
  "tags": ["标签1", "标签2", "标签3"]
}
```"""

/**
 * AI提示词常量
 */
object AiPrompts {
    val CLASSIFICATION_PROMPT = """
        请分析这张图片的类型，只返回以下三个类别之一：
        - Text-Heavy: 如果图片主要包含大量文字内容（如文档、网页、聊天记录等）
        - Rich-Content: 如果图片包含文字和图像的混合内容（如社交媒体截图、应用界面等）
        - Simple-Image: 如果图片主要是简单的图像内容，文字较少（如照片、图标等）

        只返回类别名称，不要其他解释。
    """.trimIndent()
}
