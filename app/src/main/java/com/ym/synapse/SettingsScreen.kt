package com.ym.synapse

import android.content.Context
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ExpandLess
import androidx.compose.material.icons.filled.ExpandMore
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import android.util.Log
import com.ym.synapse.KEY_API_KEY
import com.ym.synapse.KEY_AI_OCR_MODEL_ID // This is correct
import com.ym.synapse.KEY_API_URL
import com.ym.synapse.PREFS_NAME
import com.ym.synapse.KEY_AI_OCR_PROMPT // Corrected import
import com.ym.synapse.DEFAULT_AI_OCR_PROMPT // Corrected import
import com.ym.synapse.DEFAULT_AI_OCR_MODEL_ID // Import the new default model ID
import com.ym.synapse.R // Ensure R class is imported
import com.ym.synapse.data.ModelConfigManager
import com.ym.synapse.data.ImageTypeConfig
import com.ym.synapse.MODEL_CONFIG_SHARED
import com.ym.synapse.MODEL_CONFIG_SEPARATE

// SharedPreferences keys are now in AppConstants.kt

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SettingsScreen() {
    val context = LocalContext.current
    val sharedPreferences = remember {
        context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    }

    var apiUrl by remember { mutableStateOf(sharedPreferences.getString(KEY_API_URL, "") ?: "") }
    var apiKey by remember { mutableStateOf(sharedPreferences.getString(KEY_API_KEY, "") ?: "") }
    var modelId by remember { mutableStateOf(sharedPreferences.getString(KEY_AI_OCR_MODEL_ID, DEFAULT_AI_OCR_MODEL_ID) ?: DEFAULT_AI_OCR_MODEL_ID) }
    var aiOcrPromptState by remember { mutableStateOf(sharedPreferences.getString(KEY_AI_OCR_PROMPT, DEFAULT_AI_OCR_PROMPT) ?: DEFAULT_AI_OCR_PROMPT) }

    var showResetDialog by remember { mutableStateOf(false) }

    // 模型配置模式状态
    var modelConfigMode by remember { mutableStateOf(ModelConfigManager.getModelConfigMode(context)) }
    var imageTypeConfigs by remember { mutableStateOf(ModelConfigManager.getAllImageTypeConfigs(context)) }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .verticalScroll(rememberScrollState())
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text("AI OCR Settings", style = MaterialTheme.typography.headlineMedium)

        OutlinedTextField(
            value = apiUrl,
            onValueChange = { apiUrl = it },
            label = { Text("API URL") },
            placeholder = { Text(stringResource(id = R.string.settings_api_url_hint)) },
            modifier = Modifier.fillMaxWidth()
        )

        OutlinedTextField(
            value = apiKey,
            onValueChange = { apiKey = it },
            label = { Text("API Key") },
            modifier = Modifier.fillMaxWidth()
        )

        OutlinedTextField(
            value = modelId,
            onValueChange = { modelId = it },
            label = { Text(stringResource(id = R.string.settings_api_model_id_label)) }, // This should now resolve if R is imported
            modifier = Modifier.fillMaxWidth()
        )

        OutlinedTextField(
            value = aiOcrPromptState, // This should now be resolved
            onValueChange = { aiOcrPromptState = it },
            label = { Text("AI OCR Prompt") },
            placeholder = { Text("e.g., Extract text accurately.") },
            modifier = Modifier.fillMaxWidth(),
            singleLine = false,
            maxLines = 5
        )

        Spacer(modifier = Modifier.height(24.dp))

        // 模型配置模式选择
        Text(
            text = "模型配置模式",
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.Medium
        )

        Spacer(modifier = Modifier.height(8.dp))

        // 单选按钮组
        Column {
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                RadioButton(
                    selected = modelConfigMode == MODEL_CONFIG_SHARED,
                    onClick = {
                        modelConfigMode = MODEL_CONFIG_SHARED
                        ModelConfigManager.setModelConfigMode(context, MODEL_CONFIG_SHARED)
                    }
                )
                Spacer(modifier = Modifier.width(8.dp))
                Column {
                    Text("共用一个模型")
                    Text(
                        text = "所有图片类型使用相同的API配置",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }

            Spacer(modifier = Modifier.height(8.dp))

            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                RadioButton(
                    selected = modelConfigMode == MODEL_CONFIG_SEPARATE,
                    onClick = {
                        modelConfigMode = MODEL_CONFIG_SEPARATE
                        ModelConfigManager.setModelConfigMode(context, MODEL_CONFIG_SEPARATE)
                    }
                )
                Spacer(modifier = Modifier.width(8.dp))
                Column {
                    Text("每个服务不同模型")
                    Text(
                        text = "为每种图片类型配置专用的API",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }

        // 如果选择分离模式，显示各类型的配置
        if (modelConfigMode == MODEL_CONFIG_SEPARATE) {
            Spacer(modifier = Modifier.height(24.dp))

            Text(
                text = "各类型模型配置",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Medium
            )

            Spacer(modifier = Modifier.height(16.dp))

            // 为每个图片类型显示配置卡片
            imageTypeConfigs.forEach { config ->
                ImageTypeConfigCard(
                    config = config,
                    onConfigChange = { updatedConfig ->
                        ModelConfigManager.saveImageTypeConfig(context, updatedConfig)
                        imageTypeConfigs = ModelConfigManager.getAllImageTypeConfigs(context)
                    }
                )
                Spacer(modifier = Modifier.height(16.dp))
            }
        }

        Button(
            onClick = {
                Log.d("SettingsScreen", "Saving API URL: $apiUrl, API Key: $apiKey, Model ID: $modelId, AI OCR Prompt: $aiOcrPromptState")
                with(sharedPreferences.edit()) {
                    putString(KEY_API_URL, apiUrl)
                    putString(KEY_API_KEY, apiKey)
                    putString(KEY_AI_OCR_MODEL_ID, modelId)
                    putString(KEY_AI_OCR_PROMPT, aiOcrPromptState)
                    apply()
                }
                // Optionally, show a Toast or Snackbar
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("Save Settings")
        }

        Spacer(modifier = Modifier.height(16.dp))

        // 配置测试按钮（仅在分离模式下显示）
        if (modelConfigMode == MODEL_CONFIG_SEPARATE) {
            OutlinedButton(
                onClick = {
                    // 测试Rich-Content配置
                    val richContentConfig = ModelConfigManager.getConfigForImageType(context, "Rich-Content")
                    val message = if (richContentConfig != null && richContentConfig.apiUrl.isNotEmpty() && richContentConfig.apiKey.isNotEmpty()) {
                        "Rich-Content 配置正常\nAPI URL: ${richContentConfig.apiUrl.take(30)}...\nModel: ${richContentConfig.modelId}\nPrompt: ${richContentConfig.prompt.take(50)}..."
                    } else {
                        "Rich-Content 配置不完整，请检查API URL和API Key"
                    }
                    android.widget.Toast.makeText(context, message, android.widget.Toast.LENGTH_LONG).show()
                },
                modifier = Modifier.fillMaxWidth(),
                colors = ButtonDefaults.outlinedButtonColors(
                    contentColor = MaterialTheme.colorScheme.primary
                )
            ) {
                Text("🧪 测试 Rich-Content 配置")
            }

            Spacer(modifier = Modifier.height(8.dp))

            OutlinedButton(
                onClick = {
                    // 测试Simple-Image配置
                    val simpleImageConfig = ModelConfigManager.getConfigForImageType(context, "Simple-Image")
                    val message = if (simpleImageConfig != null && simpleImageConfig.apiUrl.isNotEmpty() && simpleImageConfig.apiKey.isNotEmpty()) {
                        "Simple-Image 配置正常\nAPI URL: ${simpleImageConfig.apiUrl.take(30)}...\nModel: ${simpleImageConfig.modelId}\nPrompt: ${simpleImageConfig.prompt.take(50)}..."
                    } else {
                        "Simple-Image 配置不完整，请检查API URL和API Key"
                    }
                    android.widget.Toast.makeText(context, message, android.widget.Toast.LENGTH_LONG).show()
                },
                modifier = Modifier.fillMaxWidth(),
                colors = ButtonDefaults.outlinedButtonColors(
                    contentColor = MaterialTheme.colorScheme.secondary
                )
            ) {
                Text("🎨 测试 Simple-Image 配置")
            }

            Spacer(modifier = Modifier.height(8.dp))

            OutlinedButton(
                onClick = {
                    // 测试Text-Heavy配置
                    val textHeavyConfig = ModelConfigManager.getConfigForImageType(context, "Text-Heavy")
                    val message = if (textHeavyConfig != null && textHeavyConfig.apiUrl.isNotEmpty() && textHeavyConfig.apiKey.isNotEmpty()) {
                        "Text-Heavy 配置正常\nAPI URL: ${textHeavyConfig.apiUrl.take(30)}...\nModel: ${textHeavyConfig.modelId}\nPrompt: ${textHeavyConfig.prompt.take(50)}..."
                    } else {
                        "Text-Heavy 配置不完整，请检查API URL和API Key"
                    }
                    android.widget.Toast.makeText(context, message, android.widget.Toast.LENGTH_LONG).show()
                },
                modifier = Modifier.fillMaxWidth(),
                colors = ButtonDefaults.outlinedButtonColors(
                    contentColor = MaterialTheme.colorScheme.tertiary
                )
            ) {
                Text("📝 测试 Text-Heavy 配置")
            }

            Spacer(modifier = Modifier.height(8.dp))
        }

        Spacer(modifier = Modifier.height(16.dp))

        // 重新初始化按钮
        OutlinedButton(
            onClick = {
                showResetDialog = true
            },
            modifier = Modifier.fillMaxWidth(),
            colors = ButtonDefaults.outlinedButtonColors(
                contentColor = MaterialTheme.colorScheme.error
            )
        ) {
            Text("🔄 重新初始化")
        }

        Text(
            text = "重新初始化将清除所有配置，重新开始设置流程",
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        // 底部间距，确保内容不被遮挡
        Spacer(modifier = Modifier.height(32.dp))
    }

    // 确认重新初始化对话框
    if (showResetDialog) {
        AlertDialog(
            onDismissRequest = { showResetDialog = false },
            title = { Text("确认重新初始化") },
            text = {
                Text("这将清除所有配置和设置，应用将重新开始初始化流程。\n\n确定要继续吗？")
            },
            confirmButton = {
                TextButton(
                    onClick = {
                        showResetDialog = false
                        Log.d("SettingsScreen", "Resetting app to initial state")

                        // 清除所有配置状态，重新开始初始化流程
                        val appStatePrefs = context.getSharedPreferences("app_state", Context.MODE_PRIVATE)
                        with(appStatePrefs.edit()) {
                            clear() // 清除所有app_state数据
                            apply()
                        }

                        // 重启应用到初始状态
                        val intent = context.packageManager.getLaunchIntentForPackage(context.packageName)
                        intent?.addFlags(android.content.Intent.FLAG_ACTIVITY_CLEAR_TOP)
                        intent?.addFlags(android.content.Intent.FLAG_ACTIVITY_NEW_TASK)
                        context.startActivity(intent)

                        // 结束当前Activity
                        if (context is android.app.Activity) {
                            context.finish()
                        }
                    },
                    colors = ButtonDefaults.textButtonColors(
                        contentColor = MaterialTheme.colorScheme.error
                    )
                ) {
                    Text("确认重新初始化")
                }
            },
            dismissButton = {
                TextButton(onClick = { showResetDialog = false }) {
                    Text("取消")
                }
            }
        )
    }
}

@Composable
fun ImageTypeConfigCard(
    config: ImageTypeConfig,
    onConfigChange: (ImageTypeConfig) -> Unit
) {
    var apiUrl by remember { mutableStateOf(config.apiUrl) }
    var apiKey by remember { mutableStateOf(config.apiKey) }
    var modelId by remember { mutableStateOf(config.modelId) }
    var prompt by remember { mutableStateOf(config.prompt) }
    var isExpanded by remember { mutableStateOf(false) }

    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // 标题行
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column {
                    Text(
                        text = config.displayName,
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Medium
                    )
                    Text(
                        text = config.type,
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }

                IconButton(onClick = { isExpanded = !isExpanded }) {
                    Icon(
                        imageVector = if (isExpanded) Icons.Default.ExpandLess else Icons.Default.ExpandMore,
                        contentDescription = if (isExpanded) "收起" else "展开"
                    )
                }
            }

            // 展开的配置字段
            if (isExpanded) {
                Spacer(modifier = Modifier.height(16.dp))

                OutlinedTextField(
                    value = apiUrl,
                    onValueChange = { apiUrl = it },
                    label = { Text("API URL") },
                    modifier = Modifier.fillMaxWidth()
                )

                Spacer(modifier = Modifier.height(8.dp))

                OutlinedTextField(
                    value = apiKey,
                    onValueChange = { apiKey = it },
                    label = { Text("API Key") },
                    modifier = Modifier.fillMaxWidth()
                )

                Spacer(modifier = Modifier.height(8.dp))

                OutlinedTextField(
                    value = modelId,
                    onValueChange = { modelId = it },
                    label = { Text("Model ID") },
                    modifier = Modifier.fillMaxWidth()
                )

                Spacer(modifier = Modifier.height(8.dp))

                OutlinedTextField(
                    value = prompt,
                    onValueChange = { prompt = it },
                    label = { Text("Prompt") },
                    modifier = Modifier.fillMaxWidth(),
                    singleLine = false,
                    maxLines = 3
                )

                Spacer(modifier = Modifier.height(16.dp))

                Button(
                    onClick = {
                        val updatedConfig = config.copy(
                            apiUrl = apiUrl,
                            apiKey = apiKey,
                            modelId = modelId,
                            prompt = prompt
                        )
                        onConfigChange(updatedConfig)
                    },
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Text("保存 ${config.displayName} 配置")
                }
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun SettingsScreenPreview() {
    // Wrap in a theme if your app uses one, for accurate preview
    // SynapseTheme { // Assuming you have a theme
        SettingsScreen()
    // }
}
