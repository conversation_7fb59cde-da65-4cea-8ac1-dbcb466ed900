package com.ym.synapse.utils

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.util.Base64
import android.util.Log
import com.ym.synapse.*
import com.ym.synapse.api.*
import com.ym.synapse.data.ModelConfigManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.HttpException
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.io.ByteArrayOutputStream
import java.io.File
import java.util.concurrent.TimeUnit

object AiOcrHelper {

    private const val TAG = "AiOcrHelper"

    private fun getRetrofitClient(apiUrl: String): AiOcrApiService {
        val baseUrl = if (apiUrl.endsWith("/")) apiUrl else "$apiUrl/"

        val logging = HttpLoggingInterceptor().apply {
            level = HttpLoggingInterceptor.Level.BODY
        }

        val client = OkHttpClient.Builder()
            .addInterceptor(logging)
            .connectTimeout(60, TimeUnit.SECONDS)
            .readTimeout(60, TimeUnit.SECONDS)
            .writeTimeout(60, TimeUnit.SECONDS)
            .build()

        return Retrofit.Builder()
            .baseUrl(baseUrl)
            .client(client)
            .addConverterFactory(GsonConverterFactory.create())
            .build()
            .create(AiOcrApiService::class.java)
    }

    private fun bitmapToBase64(bitmap: Bitmap): String {
        val byteArrayOutputStream = ByteArrayOutputStream()
        bitmap.compress(Bitmap.CompressFormat.JPEG, 80, byteArrayOutputStream)
        val byteArray = byteArrayOutputStream.toByteArray()
        return Base64.encodeToString(byteArray, Base64.NO_WRAP)
    }

    suspend fun classifyImage(context: Context, imagePath: String): String {
        return withContext(Dispatchers.IO) {
            try {
                // 1. Read settings
                val sharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
                val apiUrl = sharedPreferences.getString(KEY_API_URL, null)
                val apiKey = sharedPreferences.getString(KEY_API_KEY, null)
                val modelId = sharedPreferences.getString(KEY_AI_OCR_MODEL_ID, DEFAULT_AI_OCR_MODEL_ID) ?: DEFAULT_AI_OCR_MODEL_ID

                if (apiUrl.isNullOrEmpty() || apiKey.isNullOrEmpty()) {
                    Log.e(TAG, "API URL or Key is not set.")
                    return@withContext "错误: 未配置API URL或密钥。"
                }

                // 2. Load bitmap and convert to Base64
                val imageFile = File(imagePath)
                if (!imageFile.exists()) {
                    Log.e(TAG, "Image file does not exist at path: $imagePath")
                    return@withContext "错误: 找不到截图文件。"
                }

                // 尝试使用ContentResolver读取文件（适配Android 11+）
                val bitmap = try {
                    BitmapFactory.decodeFile(imagePath)
                } catch (e: Exception) {
                    Log.e(TAG, "Failed to decode bitmap from path: $imagePath", e)
                    // 尝试使用MediaStore读取
                    try {
                        val uri = android.provider.MediaStore.Images.Media.EXTERNAL_CONTENT_URI
                        val projection = arrayOf(android.provider.MediaStore.Images.Media._ID)
                        val selection = "${android.provider.MediaStore.Images.Media.DATA} = ?"
                        val selectionArgs = arrayOf(imagePath)

                        context.contentResolver.query(uri, projection, selection, selectionArgs, null)?.use { cursor ->
                            if (cursor.moveToFirst()) {
                                val id = cursor.getLong(cursor.getColumnIndexOrThrow(android.provider.MediaStore.Images.Media._ID))
                                val imageUri = android.content.ContentUris.withAppendedId(uri, id)
                                context.contentResolver.openInputStream(imageUri)?.use { inputStream ->
                                    BitmapFactory.decodeStream(inputStream)
                                }
                            } else null
                        }
                    } catch (e2: Exception) {
                        Log.e(TAG, "Failed to read image via MediaStore", e2)
                        null
                    }
                }

                if (bitmap == null) {
                    Log.e(TAG, "Failed to decode bitmap from path: $imagePath")
                    return@withContext "错误: 无法读取截图图片，可能是权限问题。"
                }
                val base64Image = bitmapToBase64(bitmap)
                val imageUrl = "data:image/jpeg;base64,$base64Image"

                // 3. Construct request with the fixed classification prompt
                val request = AiOcrRequest(
                    model = modelId,
                    messages = listOf(
                        Message(
                            role = "user",
                            content = listOf(
                                ContentPart(type = "text", text = AiPrompts.CLASSIFICATION_PROMPT),
                                ContentPart(type = "image_url", image_url = ImageUrl(url = imageUrl))
                            )
                        )
                    ),
                    max_tokens = 50 // Lower max_tokens for classification
                )

                // 4. Make API call
                val service = getRetrofitClient(apiUrl)
                val fullUrl = if (apiUrl.endsWith("/chat/completions")) apiUrl else apiUrl.removeSuffix("/") + "/chat/completions"
                val response = service.recognizeImage(
                    fullUrl = fullUrl,
                    authorization = "Bearer $apiKey",
                    request = request
                )

                // 5. Process response
                val resultText = response.choices?.firstOrNull()?.message?.content?.trim() ?: "未返回任何类别。"
                bitmap.recycle()
                resultText

            } catch (e: HttpException) {
                val errorBody = e.response()?.errorBody()?.string()
                Log.e(TAG, "AI classification failed with HTTP exception: ${e.code()} - $errorBody", e)
                "错误: ${e.code()} - $errorBody"
            } catch (e: Exception) {
                Log.e(TAG, "AI classification failed with an exception.", e)
                "错误: ${e.message}"
            }
        }
    }

    /**
     * 根据图片类型处理图片
     */
    suspend fun processImageByType(context: Context, imagePath: String, imageType: String): String {
        return withContext(Dispatchers.IO) {
            try {
                // 获取对应类型的配置，如果分类失败则使用默认配置
                val actualImageType = if (imageType.startsWith("错误:")) {
                    Log.w(TAG, "Image classification failed: $imageType, using default type")
                    "Rich-Content" // 使用默认类型
                } else {
                    imageType
                }

                val config = ModelConfigManager.getConfigForImageType(context, actualImageType)
                if (config == null) {
                    Log.e(TAG, "No configuration found for image type: $actualImageType")
                    // 尝试使用共享配置
                    val sharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
                    val fallbackApiUrl = sharedPreferences.getString(KEY_API_URL, "") ?: ""
                    val fallbackApiKey = sharedPreferences.getString(KEY_API_KEY, "") ?: ""
                    val fallbackModelId = sharedPreferences.getString(KEY_AI_OCR_MODEL_ID, DEFAULT_AI_OCR_MODEL_ID) ?: DEFAULT_AI_OCR_MODEL_ID

                    if (fallbackApiUrl.isEmpty() || fallbackApiKey.isEmpty()) {
                        return@withContext "错误: 未配置API URL或密钥，请在设置中配置。"
                    }

                    // 创建临时配置
                    val tempConfig = com.ym.synapse.data.ImageTypeConfig(
                        type = actualImageType,
                        displayName = actualImageType,
                        apiUrl = fallbackApiUrl,
                        apiKey = fallbackApiKey,
                        modelId = fallbackModelId,
                        prompt = DEFAULT_AI_OCR_PROMPT
                    )

                    return@withContext processWithConfig(context, imagePath, tempConfig, actualImageType)
                }

                if (config.apiUrl.isEmpty() || config.apiKey.isEmpty()) {
                    Log.e(TAG, "API URL or Key is not set for image type: $actualImageType")
                    return@withContext "错误: $actualImageType 类型未配置API URL或密钥。"
                }

                return@withContext processWithConfig(context, imagePath, config, actualImageType)
            } catch (e: Exception) {
                Log.e(TAG, "AI processing failed for $imageType with an exception.", e)
                "错误: ${e.message}"
            }
        }
    }

    /**
     * 使用指定配置处理图片
     */
    private suspend fun processWithConfig(
        context: Context,
        imagePath: String,
        config: com.ym.synapse.data.ImageTypeConfig,
        imageType: String
    ): String {
        return withContext(Dispatchers.IO) {
            try {
                // 加载图片并转换为Base64
                val imageFile = File(imagePath)
                if (!imageFile.exists()) {
                    Log.e(TAG, "Image file does not exist at path: $imagePath")
                    return@withContext "错误: 找不到截图文件。"
                }

                // 尝试使用ContentResolver读取文件（适配Android 11+）
                val bitmap = try {
                    BitmapFactory.decodeFile(imagePath)
                } catch (e: Exception) {
                    Log.e(TAG, "Failed to decode bitmap from path: $imagePath", e)
                    // 尝试使用MediaStore读取
                    try {
                        val uri = android.provider.MediaStore.Images.Media.EXTERNAL_CONTENT_URI
                        val projection = arrayOf(android.provider.MediaStore.Images.Media._ID)
                        val selection = "${android.provider.MediaStore.Images.Media.DATA} = ?"
                        val selectionArgs = arrayOf(imagePath)

                        context.contentResolver.query(uri, projection, selection, selectionArgs, null)?.use { cursor ->
                            if (cursor.moveToFirst()) {
                                val id = cursor.getLong(cursor.getColumnIndexOrThrow(android.provider.MediaStore.Images.Media._ID))
                                val imageUri = android.content.ContentUris.withAppendedId(uri, id)
                                context.contentResolver.openInputStream(imageUri)?.use { inputStream ->
                                    BitmapFactory.decodeStream(inputStream)
                                }
                            } else null
                        }
                    } catch (e2: Exception) {
                        Log.e(TAG, "Failed to read image via MediaStore", e2)
                        null
                    }
                }

                if (bitmap == null) {
                    Log.e(TAG, "Failed to decode bitmap from path: $imagePath")
                    return@withContext "错误: 无法读取截图图片，可能是权限问题。"
                }
                val base64Image = bitmapToBase64(bitmap)
                val imageUrl = "data:image/jpeg;base64,$base64Image"

                // 构建请求，根据图片类型设置不同的 max_tokens
                val maxTokens = when (imageType) {
                    "Text-Heavy" -> 2000 // 文字密集型需要更多tokens
                    "Rich-Content" -> 1500 // 富内容型需要中等tokens
                    "Simple-Image" -> 800 // 简单图片型需要较少tokens
                    else -> 1000 // 默认值
                }

                val request = AiOcrRequest(
                    model = config.modelId.ifEmpty { DEFAULT_AI_OCR_MODEL_ID },
                    messages = listOf(
                        Message(
                            role = "user",
                            content = listOf(
                                ContentPart(type = "text", text = config.prompt.ifEmpty { DEFAULT_AI_OCR_PROMPT }),
                                ContentPart(type = "image_url", image_url = ImageUrl(url = imageUrl))
                            )
                        )
                    ),
                    max_tokens = maxTokens
                )

                // 发起API调用
                val service = getRetrofitClient(config.apiUrl)
                val fullUrl = if (config.apiUrl.endsWith("/chat/completions")) config.apiUrl else config.apiUrl.removeSuffix("/") + "/chat/completions"
                val response = service.recognizeImage(
                    fullUrl = fullUrl,
                    authorization = "Bearer ${config.apiKey}",
                    request = request
                )

                // 处理响应
                val resultText = response.choices?.firstOrNull()?.message?.content?.trim() ?: "未返回任何内容。"
                bitmap.recycle()

                Log.d(TAG, "Processed $imageType image successfully")
                resultText

            } catch (e: HttpException) {
                val errorBody = e.response()?.errorBody()?.string()
                Log.e(TAG, "AI processing failed for $imageType with HTTP exception: ${e.code()} - $errorBody", e)
                "错误: ${e.code()} - $errorBody"
            } catch (e: Exception) {
                Log.e(TAG, "AI processing failed for $imageType with an exception.", e)
                "错误: ${e.message}"
            }
        }
    }
}
