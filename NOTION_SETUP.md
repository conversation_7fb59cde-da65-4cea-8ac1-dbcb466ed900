# Notion集成设置指南

## 1. 创建Notion Integration

1. 访问 [Notion Developers](https://www.notion.so/my-integrations)
2. 点击 "New integration"
3. 填写基本信息：
   - Name: Synapse AI OCR
   - Logo: 可选
   - Associated workspace: 选择你的工作区
4. 点击 "Submit"
5. 复制 "Internal Integration Token"（以 `secret_` 开头）

## 2. 创建Notion数据库

1. 在Notion中创建一个新页面
2. 添加一个数据库（Database）
3. 设置以下属性（Properties）：
   - **标题** (Title) - 页面标题
   - **摘要** (Text) - 内容摘要
   - **标签** (Multi-select) - 分类标签
   - **创建时间** (Created time) - 创建时间
   - **类型** (Select) - 图片类型，选项：Text-Heavy, Rich-Content, Simple-Image

**重要**: 属性名称必须与上面完全一致（包括中文），否则可能导致创建失败。

## 3. 获取Database ID

1. 打开你创建的数据库页面
2. 复制页面URL，格式类似：
   ```
   https://www.notion.so/workspace/DatabaseName-32位字符串?v=...
   ```
3. Database ID就是URL中的32位字符串

**示例**:
- URL: `https://www.notion.so/myworkspace/MyDatabase-a1b2c3d4e5f6789012345678901234ab?v=...`
- Database ID: `a1b2c3d4e5f6789012345678901234ab`

**注意**:
- 可以包含或不包含连字符，应用会自动处理格式
- 确保复制完整的32位字符串

## 4. 授权Integration访问数据库

1. 在数据库页面，点击右上角的 "..."
2. 选择 "Add connections"
3. 搜索并选择你创建的Integration（Synapse AI OCR）
4. 点击 "Confirm"

## 5. 在应用中配置

1. 打开Synapse应用
2. 进入设置页面
3. 找到"Notion集成配置"部分
4. 填入：
   - **Notion Integration Token**: 步骤1中复制的token
   - **Database ID**: 步骤3中获取的ID
5. 启用"启用Notion集成"开关
6. 可选：启用"自动同步"开关
7. 点击"测试Notion连接"验证配置
8. 保存设置

## 6. 使用方法

### 自动同步
- 启用自动同步后，每次截图分析完成会自动发送到Notion

### 手动发送
- 在历史记录中，点击记录右上角的发送按钮
- 在结果详情页面，点击顶部的发送按钮

## 7. 故障排除

### 连接测试失败

#### 401 认证失败
- **原因**: Integration Token不正确
- **解决**:
  1. 确认Token以`secret_`开头
  2. 重新复制Token，确保没有多余空格
  3. 确认Token来自正确的Integration

#### 404 数据库未找到
- **原因**: Database ID不正确或Integration未被授权
- **解决**:
  1. 重新复制Database ID（32位字符串）
  2. 确认已在数据库中添加Integration连接
  3. 检查数据库是否存在且可访问

#### 403 权限不足
- **原因**: Integration没有访问数据库的权限
- **解决**:
  1. 在数据库页面点击"..."菜单
  2. 选择"Add connections"
  3. 添加你的Integration

#### 400 请求格式错误
- **原因**: 数据库属性不匹配
- **解决**:
  1. 确认数据库有以下属性：
     - **标题** (Title类型)
     - **摘要** (Text类型)
     - **标签** (Multi-select类型)
  2. 属性名称必须完全一致（包括中文）

### 调试步骤

1. **检查日志**: 在Android Studio的Logcat中查看详细错误信息
2. **验证配置**:
   - Token格式: `secret_xxxxxxxxxx`
   - Database ID: 32位字符串
3. **测试权限**: 在Notion中手动创建一个页面到该数据库
4. **网络检查**: 确认设备可以访问notion.com

### 常见错误代码
- **401**: Token错误
- **403**: 权限不足
- **404**: 数据库不存在或ID错误
- **400**: 数据格式或属性不匹配

## 8. 数据格式

发送到Notion的数据包含：
- **标题**: AI生成的15字以内标题
- **摘要**: 100-300字的详细内容总结
- **标签**: 根据内容自动生成的分类标签
- **行动项**: 从内容中提取的待办事项（作为页面内容）

**注意**: 由于Notion API限制，暂不支持直接上传图片文件。如需图片，建议：
1. 使用其他图床服务上传图片
2. 在Notion页面中手动添加图片
3. 或者保留本地图片文件作为参考

## 9. 隐私说明

- 图片和分析结果会发送到Notion服务器
- 请确保你有权限分享这些内容
- 建议不要处理包含敏感信息的截图
